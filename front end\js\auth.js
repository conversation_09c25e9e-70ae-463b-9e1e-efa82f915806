// Auth functions
function checkAuth() {
    const user = JSON.parse(localStorage.getItem('currentUser'));
    const currentPage = window.location.pathname.split('/').pop();

    // If user is not logged in
    if (!user) {
        // Allow access only to register.html and login.html
        if (currentPage !== 'register.html' && currentPage !== 'login.html') {
            window.location.href = 'login.html';
            return null;
        }
    } else {
        // User is logged in
        if (currentPage === 'register.html' || currentPage === 'login.html') {
            // Redirect to index.html if trying to access login or register pages while logged in
            window.location.href = 'index.html';
            return user;
        }
    }

    return user;
}

// Call checkAuth on every page load
document.addEventListener('DOMContentLoaded', checkAuth);

// Handle Register Form
const registerForm = document.querySelector('.register-form');
if (registerForm) {
    registerForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const fullName = document.getElementById('fullName').value;
        const email = document.getElementById('email').value;
        const phone = document.getElementById('phone').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // Validate phone number
        const phoneRegex = /^[0-9]{11}$/;
        if (!phoneRegex.test(phone)) {
            showAlert('Please enter a valid phone number (11 digits)', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showAlert('Passwords do not match!', 'error');
            return;
        }

        // Register user with backend and send welcome Telegram message
        registerUserWithBackend(fullName, email, phone, password);
    });
}

// Handle Login Form
const loginForm = document.querySelector('.login-form');
if (loginForm) {
    loginForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const user = users.find(u => u.email === email && u.password === password);

        if (user) {
            localStorage.setItem('currentUser', JSON.stringify({
                fullName: user.fullName,
                email: user.email,
                phone: user.phone  // Store phone number for Telegram notifications
            }));

            // Set user session in backend for Telegram notifications
            setUserSessionInBackend(user);

            showAlert('Login successful! Redirecting to home page...', 'success');
            setTimeout(() => window.location.href = 'index.html', 1500);
        } else {
            showAlert('Invalid credentials!', 'error');
        }
    });
}

// Function to register user with backend and send welcome Telegram message
async function registerUserWithBackend(fullName, email, phone, password) {
    try {
        // Show loading message
        showAlert('Registering user and sending welcome message...', 'loading');

        // Check local storage for existing users first
        const users = JSON.parse(localStorage.getItem('users') || '[]');

        if (users.some(user => user.email === email)) {
            showAlert('Email already registered!', 'error');
            return;
        }

        if (users.some(user => user.phone === phone)) {
            showAlert('Phone number already registered!', 'error');
            return;
        }

        // Register with backend
        const response = await fetch('http://localhost:5001/register_user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                fullName: fullName,
                email: email,
                phone: phone,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Save to local storage
            const newUser = { fullName, email, phone, password };
            users.push(newUser);
            localStorage.setItem('users', JSON.stringify(users));

            // Show Telegram setup instructions
            showTelegramSetupModal(data.verification_code, data.telegram_bot_link);
        } else {
            showAlert(data.message || 'Registration failed', 'error');
        }
    } catch (error) {
        console.error('Error registering user:', error);
        showAlert('Registration failed. Please try again.', 'error');
    }
}

// Function to show Telegram setup modal
function showTelegramSetupModal(verificationCode, botLink) {
    // Create modal HTML
    const modalHTML = `
        <div id="telegramModal" class="telegram-modal">
            <div class="telegram-modal-content">
                <div class="telegram-header">
                    <h2>🛡️ Activate Telegram Notifications</h2>
                    <p>Complete your Vision Guard setup in just one click!</p>
                </div>

                <div class="telegram-steps">
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Click the button below to open Telegram</h3>
                            <p>This will automatically connect your account</p>
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Tap "START" in the Telegram bot</h3>
                            <p>Your account will be instantly activated</p>
                        </div>
                    </div>

                    <div class="step-item">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Start receiving security alerts!</h3>
                            <p>You'll get instant notifications for any violence detected</p>
                        </div>
                    </div>
                </div>

                <div class="telegram-actions">
                    <a href="${botLink}" target="_blank" class="telegram-btn primary">
                        <i class="fab fa-telegram"></i>
                        Open Vision Guard Bot
                    </a>

                    <div class="verification-info">
                        <p><strong>Verification Code:</strong> <code>${verificationCode}</code></p>
                        <small>This code is automatically included in the link above</small>
                    </div>
                </div>

                <div class="telegram-footer">
                    <button onclick="skipTelegramSetup()" class="skip-btn">Skip for now</button>
                    <button onclick="proceedToLogin()" class="continue-btn">I've activated Telegram</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add modal styles
    addTelegramModalStyles();
}

// Function to add Telegram modal styles
function addTelegramModalStyles() {
    const styles = `
        <style id="telegramModalStyles">
            .telegram-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            }

            .telegram-modal-content {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                padding: 30px;
                max-width: 500px;
                width: 90%;
                max-height: 90vh;
                overflow-y: auto;
                color: white;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                animation: slideUp 0.3s ease;
            }

            .telegram-header {
                text-align: center;
                margin-bottom: 30px;
            }

            .telegram-header h2 {
                margin: 0 0 10px 0;
                font-size: 24px;
                font-weight: 700;
            }

            .telegram-header p {
                margin: 0;
                opacity: 0.9;
                font-size: 16px;
            }

            .telegram-steps {
                margin-bottom: 30px;
            }

            .step-item {
                display: flex;
                align-items: flex-start;
                margin-bottom: 20px;
                padding: 15px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                backdrop-filter: blur(10px);
            }

            .step-number {
                background: #4CAF50;
                color: white;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                margin-right: 15px;
                flex-shrink: 0;
            }

            .step-content h3 {
                margin: 0 0 5px 0;
                font-size: 16px;
                font-weight: 600;
            }

            .step-content p {
                margin: 0;
                opacity: 0.8;
                font-size: 14px;
            }

            .telegram-actions {
                text-align: center;
                margin-bottom: 30px;
            }

            .telegram-btn {
                display: inline-block;
                background: #0088cc;
                color: white;
                padding: 15px 30px;
                border-radius: 50px;
                text-decoration: none;
                font-weight: 600;
                font-size: 16px;
                transition: all 0.3s ease;
                margin-bottom: 20px;
            }

            .telegram-btn:hover {
                background: #006699;
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0, 136, 204, 0.3);
            }

            .telegram-btn i {
                margin-right: 10px;
                font-size: 18px;
            }

            .verification-info {
                background: rgba(255, 255, 255, 0.1);
                padding: 15px;
                border-radius: 10px;
                backdrop-filter: blur(10px);
            }

            .verification-info code {
                background: rgba(255, 255, 255, 0.2);
                padding: 5px 10px;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-weight: bold;
                font-size: 16px;
            }

            .telegram-footer {
                display: flex;
                justify-content: space-between;
                gap: 15px;
            }

            .skip-btn, .continue-btn {
                flex: 1;
                padding: 12px 20px;
                border: none;
                border-radius: 25px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .skip-btn {
                background: rgba(255, 255, 255, 0.2);
                color: white;
            }

            .skip-btn:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .continue-btn {
                background: #4CAF50;
                color: white;
            }

            .continue-btn:hover {
                background: #45a049;
                transform: translateY(-1px);
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            @media (max-width: 600px) {
                .telegram-modal-content {
                    padding: 20px;
                    margin: 20px;
                }

                .telegram-footer {
                    flex-direction: column;
                }
            }
        </style>
    `;

    document.head.insertAdjacentHTML('beforeend', styles);
}

// Function to skip Telegram setup
function skipTelegramSetup() {
    closeTelegramModal();
    showAlert('Registration successful! You can set up Telegram notifications later.', 'success');
    setTimeout(() => window.location.href = 'login.html', 2000);
}

// Function to proceed to login after Telegram setup
function proceedToLogin() {
    closeTelegramModal();
    showAlert('Registration successful! Please login to continue.', 'success');
    setTimeout(() => window.location.href = 'login.html', 1500);
}

// Function to close Telegram modal
function closeTelegramModal() {
    const modal = document.getElementById('telegramModal');
    const styles = document.getElementById('telegramModalStyles');

    if (modal) modal.remove();
    if (styles) styles.remove();
}

// Function to set user session in backend for Telegram notifications
async function setUserSessionInBackend(user) {
    try {
        const response = await fetch('http://localhost:5001/set_user_session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: user.phone,
                full_name: user.fullName,
                email: user.email
            })
        });

        const data = await response.json();
        if (response.ok) {
            console.log('User session set in backend for Telegram notifications');
        } else {
            console.error('Failed to set user session in backend:', data.message);
        }
    } catch (error) {
        console.error('Error setting user session in backend:', error);
    }
}

// Logout functionality
const logoutBtn = document.getElementById('logoutBtn');
if (logoutBtn) {
    logoutBtn.addEventListener('click', (e) => {
        e.preventDefault();
        localStorage.removeItem('currentUser');
        // Create and show alert
        const alert = document.createElement('div');
        alert.className = 'alert alert-success fade-in show';
        alert.textContent = 'Logged out successfully!';
        document.body.appendChild(alert);

        // Redirect after delay
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1000);
    });
}

// Enhanced Alert function with better styling
function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} fade-in`;
    alert.textContent = message;

    const form = document.querySelector('form');
    form.parentNode.insertBefore(alert, form);

    // Add fade-in animation
    setTimeout(() => alert.classList.add('show'), 100);

    // Remove alert after delay
    setTimeout(() => {
        alert.classList.remove('show');
        setTimeout(() => alert.remove(), 300);
    }, 3000);
}

// Password visibility toggle
document.addEventListener('DOMContentLoaded', function () {
    const passwordToggles = document.querySelectorAll('.password-toggle');

    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function (e) {
            e.preventDefault();

            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');

            // Toggle password visibility
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                this.classList.add('showing');

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                    this.classList.remove('showing');
                }, 3000);
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                this.classList.remove('showing');
            }

            // Add ripple effect
            const ripple = document.createElement('div');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 1000);
        });
    });
});

