// Auth functions
function checkAuth() {
    const user = JSON.parse(localStorage.getItem('currentUser'));
    const currentPage = window.location.pathname.split('/').pop();

    // If user is not logged in
    if (!user) {
        // Allow access only to register.html and login.html
        if (currentPage !== 'register.html' && currentPage !== 'login.html') {
            window.location.href = 'login.html';
            return null;
        }
    } else {
        // User is logged in
        if (currentPage === 'register.html' || currentPage === 'login.html') {
            // Redirect to index.html if trying to access login or register pages while logged in
            window.location.href = 'index.html';
            return user;
        }
    }

    return user;
}

// Call checkAuth on every page load
document.addEventListener('DOMContentLoaded', checkAuth);

// Handle Register Form
const registerForm = document.querySelector('.register-form');
if (registerForm) {
    registerForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const fullName = document.getElementById('fullName').value;
        const email = document.getElementById('email').value;
        const phone = document.getElementById('phone').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // Validate phone number
        const phoneRegex = /^[0-9]{11}$/;
        if (!phoneRegex.test(phone)) {
            showAlert('Please enter a valid phone number (11 digits)', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showAlert('Passwords do not match!', 'error');
            return;
        }

        // Register user with backend and send welcome Telegram message
        registerUserWithBackend(fullName, email, phone, password);
    });
}

// Handle Login Form
const loginForm = document.querySelector('.login-form');
if (loginForm) {
    loginForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const user = users.find(u => u.email === email && u.password === password);

        if (user) {
            localStorage.setItem('currentUser', JSON.stringify({
                fullName: user.fullName,
                email: user.email,
                phone: user.phone  // Store phone number for Telegram notifications
            }));

            // Set user session in backend for Telegram notifications
            setUserSessionInBackend(user);

            showAlert('Login successful! Redirecting to home page...', 'success');
            setTimeout(() => window.location.href = 'index.html', 1500);
        } else {
            showAlert('Invalid credentials!', 'error');
        }
    });
}

// Function to register user with backend and send welcome Telegram message
async function registerUserWithBackend(fullName, email, phone, password) {
    try {
        // Show loading message
        showAlert('Registering user and sending welcome message...', 'loading');

        // Check local storage for existing users first
        const users = JSON.parse(localStorage.getItem('users') || '[]');

        if (users.some(user => user.email === email)) {
            showAlert('Email already registered!', 'error');
            return;
        }

        if (users.some(user => user.phone === phone)) {
            showAlert('Phone number already registered!', 'error');
            return;
        }

        // Register with backend
        const response = await fetch('http://localhost:5001/register_user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                fullName: fullName,
                email: email,
                phone: phone,
                password: password
            })
        });

        const data = await response.json();

        if (response.ok) {
            // Save to local storage
            const newUser = { fullName, email, phone, password };
            users.push(newUser);
            localStorage.setItem('users', JSON.stringify(users));

            // Show success message with Telegram status
            let message = 'Registration successful!';
            if (data.telegram_sent) {
                message += ' Welcome message sent to your Telegram.';
            } else {
                message += ' Please configure Telegram for notifications.';
            }
            message += ' Redirecting to login page...';

            showAlert(message, 'success');
            setTimeout(() => window.location.href = 'login.html', 2500);
        } else {
            showAlert(data.message || 'Registration failed', 'error');
        }
    } catch (error) {
        console.error('Error registering user:', error);
        showAlert('Registration failed. Please try again.', 'error');
    }
}

// Function to set user session in backend for Telegram notifications
async function setUserSessionInBackend(user) {
    try {
        const response = await fetch('http://localhost:5001/set_user_session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                phone_number: user.phone,
                full_name: user.fullName,
                email: user.email
            })
        });

        const data = await response.json();
        if (response.ok) {
            console.log('User session set in backend for Telegram notifications');
        } else {
            console.error('Failed to set user session in backend:', data.message);
        }
    } catch (error) {
        console.error('Error setting user session in backend:', error);
    }
}

// Logout functionality
const logoutBtn = document.getElementById('logoutBtn');
if (logoutBtn) {
    logoutBtn.addEventListener('click', (e) => {
        e.preventDefault();
        localStorage.removeItem('currentUser');
        // Create and show alert
        const alert = document.createElement('div');
        alert.className = 'alert alert-success fade-in show';
        alert.textContent = 'Logged out successfully!';
        document.body.appendChild(alert);

        // Redirect after delay
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1000);
    });
}

// Enhanced Alert function with better styling
function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} fade-in`;
    alert.textContent = message;

    const form = document.querySelector('form');
    form.parentNode.insertBefore(alert, form);

    // Add fade-in animation
    setTimeout(() => alert.classList.add('show'), 100);

    // Remove alert after delay
    setTimeout(() => {
        alert.classList.remove('show');
        setTimeout(() => alert.remove(), 300);
    }, 3000);
}

// Password visibility toggle
document.addEventListener('DOMContentLoaded', function () {
    const passwordToggles = document.querySelectorAll('.password-toggle');

    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function (e) {
            e.preventDefault();

            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');

            // Toggle password visibility
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                this.classList.add('showing');

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                    this.classList.remove('showing');
                }, 3000);
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                this.classList.remove('showing');
            }

            // Add ripple effect
            const ripple = document.createElement('div');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 1000);
        });
    });
});

