<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Setup - Vision Guard</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/styles.css">
</head>

<body>
    <header class="header">
        <nav class="nav">
            <a href="index.html" class="logo">Vision Guard</a>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="upload.html">Upload Video</a>
                <a href="realtime.html">Real-time Detection</a>
                <a href="telegram-setup.html">Telegram Setup</a>
                <a href="#" id="logoutBtn">Logout</a>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <div class="content-container">
            <div class="glass-card">
                <h2>Telegram Emergency Notifications Setup</h2>

                <div class="setup-instructions">
                    <h3>📱 Quick Setup Guide</h3>
                    <ol>
                        <li>Create a bot by messaging <strong>@BotFather</strong> on Telegram</li>
                        <li>Get your Chat ID by messaging <strong>@userinfobot</strong> on Telegram</li>
                        <li>Enter your bot token and chat ID below</li>
                        <li>Test the connection</li>
                    </ol>
                </div>

                <div class="telegram-config">
                    <h3>🔧 Configuration</h3>

                    <div class="form-group">
                        <label for="botToken">Bot Token</label>
                        <input type="text" id="botToken" placeholder="Enter your Telegram bot token">
                        <small>Get this from @BotFather after creating your bot</small>
                    </div>

                    <div class="form-group">
                        <label for="chatId">Chat ID</label>
                        <input type="text" id="chatId" placeholder="Enter your Telegram chat ID">
                        <small>Get this from @userinfobot</small>
                    </div>

                    <div class="form-group">
                        <label for="locationName">Location Name</label>
                        <input type="text" id="locationName" placeholder="Security Camera Location"
                            value="Security Camera Location">
                        <small>This will appear in emergency alerts</small>
                    </div>

                    <div class="controls">
                        <button id="findChatIdBtn" class="control-btn">
                            <i class="fas fa-search"></i> Find My Chat ID
                        </button>
                        <button id="saveConfigBtn" class="control-btn">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                        <button id="testTelegramBtn" class="control-btn">
                            <i class="fas fa-paper-plane"></i> Test Connection
                        </button>
                        <button id="mapPhoneChatBtn" class="control-btn">
                            <i class="fas fa-link"></i> Link Phone to Chat
                        </button>
                    </div>
                </div>

                <div id="status" class="status-message"></div>

                <div class="current-config">
                    <h3>📋 Current Configuration</h3>
                    <div id="configDisplay">
                        <p>No configuration set</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="js/auth.js"></script>
    <script>
        // Check if user is logged in
        document.addEventListener('DOMContentLoaded', () => {
            const user = JSON.parse(localStorage.getItem('currentUser'));
            if (!user) {
                window.location.href = 'login.html';
                return;
            }

            // Load current configuration
            loadCurrentConfig();
        });

        const API_BASE_URL = 'http://localhost:5001';

        async function loadCurrentConfig() {
            try {
                const response = await fetch(`${API_BASE_URL}/get_user_session`);
                const data = await response.json();

                if (response.ok && data.session) {
                    const configDisplay = document.getElementById('configDisplay');
                    configDisplay.innerHTML = `
                        <p><strong>Phone:</strong> ${data.session.phone_number || 'Not set'}</p>
                        <p><strong>Location:</strong> ${data.session.location_name || 'Security Camera Location'}</p>
                        <p><strong>Telegram:</strong> ${data.session.phone_number ? 'Configured' : 'Not configured'}</p>
                    `;
                }
            } catch (error) {
                console.error('Error loading config:', error);
            }
        }

        document.getElementById('saveConfigBtn').addEventListener('click', async () => {
            const botToken = document.getElementById('botToken').value;
            const chatId = document.getElementById('chatId').value;
            const locationName = document.getElementById('locationName').value;
            const status = document.getElementById('status');

            if (!botToken || !chatId) {
                status.textContent = 'Please enter both bot token and chat ID';
                status.className = 'status-message error';
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/set_user_session`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        telegram_chat_id: chatId,
                        location_name: locationName
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    status.textContent = 'Configuration saved successfully!';
                    status.className = 'status-message success';
                    loadCurrentConfig();
                } else {
                    status.textContent = data.message || 'Failed to save configuration';
                    status.className = 'status-message error';
                }
            } catch (error) {
                status.textContent = 'Error saving configuration';
                status.className = 'status-message error';
            }
        });

        document.getElementById('findChatIdBtn').addEventListener('click', async () => {
            const status = document.getElementById('status');
            status.textContent = 'Looking for your Chat ID...';
            status.className = 'status-message loading';

            try {
                const response = await fetch(`${API_BASE_URL}/get_telegram_updates`);
                const data = await response.json();

                if (response.ok && data.updates && data.updates.length > 0) {
                    const chatId = data.updates[0].chat_id;
                    document.getElementById('chatId').value = chatId;
                    status.textContent = `Found your Chat ID: ${chatId}`;
                    status.className = 'status-message success';
                } else {
                    status.textContent = 'No messages found. Please send a message to your bot first, then try again.';
                    status.className = 'status-message error';
                }
            } catch (error) {
                status.textContent = 'Error finding Chat ID';
                status.className = 'status-message error';
            }
        });

        document.getElementById('testTelegramBtn').addEventListener('click', async () => {
            const status = document.getElementById('status');
            status.textContent = 'Sending test message...';
            status.className = 'status-message loading';

            try {
                const response = await fetch(`${API_BASE_URL}/test_telegram`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: 'Test message from Vision Guard security system. If you receive this, your Telegram notifications are working correctly!'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    status.textContent = 'Test message sent successfully! Check your Telegram.';
                    status.className = 'status-message success';
                } else {
                    status.textContent = data.message || 'Failed to send test message';
                    status.className = 'status-message error';
                }
            } catch (error) {
                status.textContent = 'Error sending test message';
                status.className = 'status-message error';
            }
        });

        document.getElementById('mapPhoneChatBtn').addEventListener('click', async () => {
            const chatId = document.getElementById('chatId').value;
            const status = document.getElementById('status');

            if (!chatId) {
                status.textContent = 'Please enter your Chat ID first';
                status.className = 'status-message error';
                return;
            }

            try {
                // Get current user info
                const user = JSON.parse(localStorage.getItem('currentUser'));
                if (!user || !user.phone) {
                    status.textContent = 'No user phone number found. Please login first.';
                    status.className = 'status-message error';
                    return;
                }

                status.textContent = 'Linking phone number to chat ID...';
                status.className = 'status-message loading';

                const response = await fetch(`${API_BASE_URL}/map_phone_to_chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        phone_number: user.phone,
                        chat_id: chatId
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    status.textContent = `Phone ${user.phone} successfully linked to Telegram!`;
                    status.className = 'status-message success';
                    loadCurrentConfig();
                } else {
                    status.textContent = data.message || 'Failed to link phone to chat';
                    status.className = 'status-message error';
                }
            } catch (error) {
                status.textContent = 'Error linking phone to chat';
                status.className = 'status-message error';
            }
        });
    </script>
</body>

</html>