#!/usr/bin/env python3
"""
Test script for Vision Guard Telegram Auto-Integration
Tests the complete flow: register → get verification code → simulate bot interaction → verify notifications
"""

import requests
import json
import time
import sys

# Configuration
BACKEND_URL = "http://localhost:5001"
TELEGRAM_BOT_TOKEN = "**********************************************"

def test_user_registration():
    """Test user registration and verification code generation"""
    print("🔍 Testing user registration...")
    
    test_user = {
        "fullName": "Test User",
        "email": "<EMAIL>",
        "phone": "01234567890",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/register_user", json=test_user)
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Registration successful!")
            print(f"   Verification Code: {data.get('verification_code')}")
            print(f"   Bot Link: {data.get('telegram_bot_link')}")
            return data.get('verification_code'), test_user['phone']
        else:
            print(f"❌ Registration failed: {data.get('message')}")
            return None, None
            
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        return None, None

def simulate_telegram_webhook(verification_code, chat_id=12345):
    """Simulate Telegram webhook call when user starts the bot"""
    print(f"🤖 Simulating Telegram bot interaction...")
    
    webhook_data = {
        "message": {
            "chat": {
                "id": chat_id,
                "username": "testuser",
                "first_name": "Test"
            },
            "text": f"/start {verification_code}",
            "message_id": 1
        }
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/telegram_webhook", json=webhook_data)
        
        if response.status_code == 200:
            print("✅ Webhook processed successfully!")
            return True
        else:
            print(f"❌ Webhook failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Webhook error: {str(e)}")
        return False

def test_telegram_status(phone_number):
    """Test if phone number is linked to Telegram"""
    print("📱 Checking Telegram link status...")
    
    try:
        response = requests.post(f"{BACKEND_URL}/check_telegram_status", 
                               json={"phone_number": phone_number})
        data = response.json()
        
        if response.status_code == 200:
            is_linked = data.get('is_linked', False)
            if is_linked:
                print("✅ Phone number successfully linked to Telegram!")
            else:
                print("❌ Phone number not linked to Telegram")
            return is_linked
        else:
            print(f"❌ Status check failed: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Status check error: {str(e)}")
        return False

def test_telegram_notification(phone_number):
    """Test sending a notification to the linked phone number"""
    print("📢 Testing Telegram notification...")
    
    try:
        # First set the user session to simulate logged in user
        session_data = {
            "phone_number": phone_number,
            "full_name": "Test User",
            "email": "<EMAIL>",
            "location_name": "Test Location"
        }
        
        requests.post(f"{BACKEND_URL}/set_user_session", json=session_data)
        
        # Send test notification
        response = requests.post(f"{BACKEND_URL}/test_telegram", 
                               json={"message": "🛡️ TEST ALERT: Vision Guard auto-integration test successful!"})
        data = response.json()
        
        if response.status_code == 200:
            print("✅ Test notification sent successfully!")
            return True
        else:
            print(f"❌ Notification failed: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Notification error: {str(e)}")
        return False

def check_backend_status():
    """Check if backend server is running"""
    print("🔍 Checking backend server status...")
    
    try:
        response = requests.get(f"{BACKEND_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running!")
            return True
        else:
            print(f"❌ Backend server returned status: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server. Make sure it's running on port 5001.")
        return False
    except Exception as e:
        print(f"❌ Backend check error: {str(e)}")
        return False

def main():
    """Run complete integration test"""
    print("🛡️ Vision Guard Telegram Auto-Integration Test")
    print("=" * 50)
    
    # Check backend status
    if not check_backend_status():
        print("\n❌ Test failed: Backend server not accessible")
        sys.exit(1)
    
    # Test registration
    verification_code, phone_number = test_user_registration()
    if not verification_code:
        print("\n❌ Test failed: Registration unsuccessful")
        sys.exit(1)
    
    print(f"\n📋 Test Details:")
    print(f"   Phone: {phone_number}")
    print(f"   Verification Code: {verification_code}")
    print(f"   Bot Link: https://t.me/Visionguard_security_bot?start={verification_code}")
    
    # Simulate webhook
    if not simulate_telegram_webhook(verification_code):
        print("\n❌ Test failed: Webhook simulation unsuccessful")
        sys.exit(1)
    
    # Check link status
    time.sleep(1)  # Give it a moment to process
    if not test_telegram_status(phone_number):
        print("\n❌ Test failed: Phone not linked after webhook")
        sys.exit(1)
    
    # Test notification
    if not test_telegram_notification(phone_number):
        print("\n❌ Test failed: Notification not sent")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 ALL TESTS PASSED!")
    print("✅ Auto-integration is working correctly!")
    print("\n📱 Next Steps:")
    print("1. Users can now register on your website")
    print("2. They'll get a verification link automatically")
    print("3. Clicking the link opens Telegram and auto-links their account")
    print("4. They'll receive instant violence detection alerts!")
    print("\n🔗 Bot Link: https://t.me/Visionguard_security_bot")

if __name__ == "__main__":
    main()
