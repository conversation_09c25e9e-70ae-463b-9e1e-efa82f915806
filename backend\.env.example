# Telegram Bot Configuration
# To set up Telegram notifications:
# 1. Create a new bot by messaging @BotFather on Telegram
# 2. Get your bot token and replace YOUR_BOT_TOKEN_HERE
# 3. Get your chat ID by messaging @userinfobot on Telegram
# 4. Copy this file to .env and fill in your actual values

TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=YOUR_NUMERIC_CHAT_ID_HERE

# Optional: Set a custom location name for alerts
LOCATION_NAME=Security Camera Location

# Optional: Set a custom website URL for alerts
WEBSITE_URL=http://localhost:5500/front%20end/realtime.html
