<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-time Detection - Violence Detection</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/styles.css">
</head>

<body>
    <header class="header">
        <nav class="nav">
            <a href="index.html" class="logo">Vison Guard</a>
            <div class="nav-links">
                <a href="index.html">Home</a>
                <a href="upload.html">Upload Video</a>
                <a href="realtime.html">Real-time Detection</a>
                <a href="#" id="logoutBtn">Logout</a>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <div class="content-container">
            <div class="glass-card">
                <h2>Real-time Violence Detection</h2>
                <div class="camera-feed-container">
                    <img id="videoFeed" src="" alt="Camera feed">
                </div>
                <div class="controls">
                    <button id="startCameraBtn" class="control-btn">
                        <i class="fas fa-video"></i> Start Camera & Auto-Record
                    </button>
                    <button id="stopCameraBtn" class="control-btn" style="display: none;">
                        <i class="fas fa-video-slash"></i> Stop Camera
                    </button>
                    <button id="refreshClipsBtn" class="control-btn" style="display: none;">
                        <i class="fas fa-refresh"></i> Refresh Clips
                    </button>
                    <button id="testTelegramBtn" class="control-btn telegram-test-btn">
                        <i class="fab fa-telegram"></i> Test Telegram Alerts
                    </button>
                </div>
                <div id="status" class="status-message"></div>

                <!-- Telegram Status Section -->
                <div id="telegramStatus" class="telegram-status-section">
                    <div class="telegram-info">
                        <i class="fab fa-telegram"></i>
                        <span>Connect Telegram to receive instant violence detection alerts with video evidence</span>
                        <a href="https://t.me/Visionguard_security_bot" target="_blank" class="bot-link">
                            Start Bot
                        </a>
                    </div>
                </div>

                <!-- Violence Clips Section -->
                <div id="violenceClipsSection" class="clips-section" style="display: none;">
                    <h3>Violence Detection Clips</h3>
                    <div id="clipsContainer" class="clips-container">
                        <!-- Clips will be dynamically loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Violence Description Modal -->
        <div id="violenceModal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <h3>Security Alert</h3>
                <div id="violenceDescription" class="violence-description"></div>
            </div>
        </div>
    </main>

    <script src="js/auth.js"></script>
    <script src="js/realtime.js"></script>
    <script>
        // Check if user is logged in
        document.addEventListener('DOMContentLoaded', () => {
            const user = JSON.parse(localStorage.getItem('currentUser'));
            if (!user) {
                // If not logged in, redirect to login page
                window.location.href = 'login.html';
            }
        });
    </script>
</body>

</html>