#!/usr/bin/env python3
"""
Setup script for Vision Guard Telegram Webhook
Configures the Telegram bot webhook URL for production deployment
"""

import requests
import sys

# Configuration
TELEGRAM_BOT_TOKEN = "**********************************************"

def setup_webhook(webhook_url):
    """Set up Telegram webhook"""
    print(f"🔧 Setting up Telegram webhook...")
    print(f"   Bot Token: {TELEGRAM_BOT_TOKEN[:10]}...")
    print(f"   Webhook URL: {webhook_url}")
    
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/setWebhook"
        payload = {'url': webhook_url}
        
        response = requests.post(url, json=payload)
        result = response.json()
        
        if response.status_code == 200 and result.get('ok'):
            print("✅ Webhook set up successfully!")
            print(f"   Description: {result.get('description', 'N/A')}")
            return True
        else:
            print(f"❌ Failed to set webhook: {result.get('description', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error setting up webhook: {str(e)}")
        return False

def get_webhook_info():
    """Get current webhook information"""
    print("📋 Getting current webhook info...")
    
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getWebhookInfo"
        response = requests.get(url)
        result = response.json()
        
        if response.status_code == 200 and result.get('ok'):
            info = result.get('result', {})
            print("✅ Webhook info retrieved:")
            print(f"   URL: {info.get('url', 'Not set')}")
            print(f"   Has Custom Certificate: {info.get('has_custom_certificate', False)}")
            print(f"   Pending Update Count: {info.get('pending_update_count', 0)}")
            print(f"   Last Error Date: {info.get('last_error_date', 'None')}")
            print(f"   Last Error Message: {info.get('last_error_message', 'None')}")
            return True
        else:
            print(f"❌ Failed to get webhook info: {result.get('description', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error getting webhook info: {str(e)}")
        return False

def delete_webhook():
    """Delete current webhook"""
    print("🗑️ Deleting current webhook...")
    
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/deleteWebhook"
        response = requests.post(url)
        result = response.json()
        
        if response.status_code == 200 and result.get('ok'):
            print("✅ Webhook deleted successfully!")
            return True
        else:
            print(f"❌ Failed to delete webhook: {result.get('description', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error deleting webhook: {str(e)}")
        return False

def test_bot_connection():
    """Test bot connection"""
    print("🤖 Testing bot connection...")
    
    try:
        url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getMe"
        response = requests.get(url)
        result = response.json()
        
        if response.status_code == 200 and result.get('ok'):
            bot_info = result.get('result', {})
            print("✅ Bot connection successful!")
            print(f"   Bot Name: {bot_info.get('first_name', 'N/A')}")
            print(f"   Username: @{bot_info.get('username', 'N/A')}")
            print(f"   Bot ID: {bot_info.get('id', 'N/A')}")
            return True
        else:
            print(f"❌ Bot connection failed: {result.get('description', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing bot connection: {str(e)}")
        return False

def main():
    """Main setup function"""
    print("🛡️ Vision Guard Telegram Webhook Setup")
    print("=" * 50)
    
    # Test bot connection first
    if not test_bot_connection():
        print("\n❌ Setup failed: Cannot connect to Telegram bot")
        sys.exit(1)
    
    print("\nChoose an option:")
    print("1. Set up webhook for production")
    print("2. Set up webhook for local testing (ngrok)")
    print("3. Get current webhook info")
    print("4. Delete current webhook")
    print("5. Exit")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    if choice == "1":
        webhook_url = input("Enter your production webhook URL (e.g., https://yourdomain.com/telegram_webhook): ").strip()
        if webhook_url:
            setup_webhook(webhook_url)
        else:
            print("❌ No URL provided")
    
    elif choice == "2":
        print("\n📋 For local testing with ngrok:")
        print("1. Install ngrok: https://ngrok.com/")
        print("2. Run: ngrok http 5001")
        print("3. Copy the HTTPS URL (e.g., https://abc123.ngrok.io)")
        print("4. Add '/telegram_webhook' to the end")
        
        webhook_url = input("\nEnter your ngrok webhook URL (e.g., https://abc123.ngrok.io/telegram_webhook): ").strip()
        if webhook_url:
            setup_webhook(webhook_url)
        else:
            print("❌ No URL provided")
    
    elif choice == "3":
        get_webhook_info()
    
    elif choice == "4":
        if input("Are you sure you want to delete the webhook? (y/N): ").lower() == 'y':
            delete_webhook()
        else:
            print("Cancelled")
    
    elif choice == "5":
        print("👋 Goodbye!")
        sys.exit(0)
    
    else:
        print("❌ Invalid choice")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed!")
    print("\n📱 Your bot is ready at: https://t.me/Visionguard_security_bot")
    print("🔗 Users will get automatic verification links during registration!")

if __name__ == "__main__":
    main()
